# Database Schema Documentation

## Overview

The Al-Jameia Financial Association Management System uses PostgreSQL as the primary database. The schema is designed to handle financial transactions, member management, loan processing, and administrative functions with strong data integrity and security.

## Core Tables

### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'member',
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP WITH TIME ZONE,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT users_role_check CHECK (role IN ('member', 'admin', 'super_admin')),
    CONSTRAINT users_status_check CHECK (status IN ('active', 'inactive', 'suspended', 'deleted'))
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
```

### User Profiles Table
```sql
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    first_name_ar VARCHAR(100),
    last_name_ar VARCHAR(100),
    phone VARCHAR(20),
    date_of_birth DATE,
    gender VARCHAR(10),
    nationality VARCHAR(100),
    id_number VARCHAR(50),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    emergency_contact_name VARCHAR(200),
    emergency_contact_phone VARCHAR(20),
    profile_picture_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT user_profiles_gender_check CHECK (gender IN ('male', 'female', 'other')),
    CONSTRAINT user_profiles_user_id_unique UNIQUE (user_id)
);

CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX idx_user_profiles_name ON user_profiles(first_name, last_name);
```

### Members Table
```sql
CREATE TABLE members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    member_number VARCHAR(20) UNIQUE NOT NULL,
    membership_type VARCHAR(50) NOT NULL DEFAULT 'regular',
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    join_date DATE NOT NULL DEFAULT CURRENT_DATE,
    termination_date DATE,
    monthly_contribution_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_contributions DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    total_loans DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    current_debt DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    credit_score INTEGER DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT members_membership_type_check CHECK (membership_type IN ('regular', 'premium', 'vip')),
    CONSTRAINT members_status_check CHECK (status IN ('active', 'inactive', 'suspended', 'terminated')),
    CONSTRAINT members_user_id_unique UNIQUE (user_id)
);

CREATE INDEX idx_members_user_id ON members(user_id);
CREATE INDEX idx_members_number ON members(member_number);
CREATE INDEX idx_members_status ON members(status);
CREATE INDEX idx_members_join_date ON members(join_date);
```

### Contributions Table
```sql
CREATE TABLE contributions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    contribution_type VARCHAR(50) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    payment_reference VARCHAR(100),
    due_date DATE,
    paid_date DATE,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    late_fee DECIMAL(10,2) DEFAULT 0.00,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT contributions_type_check CHECK (contribution_type IN ('monthly', 'annual', 'special', 'penalty')),
    CONSTRAINT contributions_payment_method_check CHECK (payment_method IN ('cash', 'bank_transfer', 'card', 'mobile_payment')),
    CONSTRAINT contributions_status_check CHECK (status IN ('pending', 'paid', 'overdue', 'cancelled')),
    CONSTRAINT contributions_amount_positive CHECK (amount > 0)
);

CREATE INDEX idx_contributions_member_id ON contributions(member_id);
CREATE INDEX idx_contributions_status ON contributions(status);
CREATE INDEX idx_contributions_due_date ON contributions(due_date);
CREATE INDEX idx_contributions_member_date ON contributions(member_id, due_date);
```

### Loans Table
```sql
CREATE TABLE loans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
    loan_number VARCHAR(20) UNIQUE NOT NULL,
    loan_type VARCHAR(50) NOT NULL,
    principal_amount DECIMAL(12,2) NOT NULL,
    interest_rate DECIMAL(5,4) NOT NULL,
    term_months INTEGER NOT NULL,
    monthly_payment DECIMAL(10,2) NOT NULL,
    remaining_balance DECIMAL(12,2) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    application_date DATE NOT NULL DEFAULT CURRENT_DATE,
    approval_date DATE,
    disbursement_date DATE,
    maturity_date DATE,
    purpose TEXT,
    collateral_description TEXT,
    guarantor_id UUID REFERENCES members(id),
    approved_by UUID REFERENCES users(id),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT loans_type_check CHECK (loan_type IN ('personal', 'emergency', 'business', 'education', 'housing')),
    CONSTRAINT loans_status_check CHECK (status IN ('pending', 'approved', 'rejected', 'active', 'completed', 'defaulted')),
    CONSTRAINT loans_amount_positive CHECK (principal_amount > 0),
    CONSTRAINT loans_rate_positive CHECK (interest_rate >= 0),
    CONSTRAINT loans_term_positive CHECK (term_months > 0)
);

CREATE INDEX idx_loans_member_id ON loans(member_id);
CREATE INDEX idx_loans_status ON loans(status);
CREATE INDEX idx_loans_number ON loans(loan_number);
CREATE INDEX idx_loans_application_date ON loans(application_date);
CREATE INDEX idx_loans_member_status ON loans(member_id, status);
```

### Loan Payments Table
```sql
CREATE TABLE loan_payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    loan_id UUID NOT NULL REFERENCES loans(id) ON DELETE CASCADE,
    payment_number INTEGER NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    principal_amount DECIMAL(10,2) NOT NULL,
    interest_amount DECIMAL(10,2) NOT NULL,
    late_fee DECIMAL(10,2) DEFAULT 0.00,
    due_date DATE NOT NULL,
    paid_date DATE,
    payment_method VARCHAR(50),
    payment_reference VARCHAR(100),
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT loan_payments_payment_method_check CHECK (payment_method IN ('cash', 'bank_transfer', 'card', 'mobile_payment')),
    CONSTRAINT loan_payments_status_check CHECK (status IN ('pending', 'paid', 'overdue', 'partial')),
    CONSTRAINT loan_payments_amount_positive CHECK (amount > 0),
    CONSTRAINT loan_payments_unique_number UNIQUE (loan_id, payment_number)
);

CREATE INDEX idx_loan_payments_loan_id ON loan_payments(loan_id);
CREATE INDEX idx_loan_payments_status ON loan_payments(status);
CREATE INDEX idx_loan_payments_due_date ON loan_payments(due_date);
```

### Documents Table
```sql
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    member_id UUID REFERENCES members(id) ON DELETE CASCADE,
    loan_id UUID REFERENCES loans(id) ON DELETE CASCADE,
    document_type VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    checksum VARCHAR(64) NOT NULL,
    is_encrypted BOOLEAN DEFAULT FALSE,
    uploaded_by UUID NOT NULL REFERENCES users(id),
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    expiry_date DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT documents_type_check CHECK (document_type IN ('id_copy', 'income_proof', 'bank_statement', 'contract', 'collateral_proof', 'other')),
    CONSTRAINT documents_status_check CHECK (status IN ('pending', 'approved', 'rejected', 'expired')),
    CONSTRAINT documents_size_positive CHECK (file_size > 0),
    CONSTRAINT documents_member_or_loan CHECK ((member_id IS NOT NULL) OR (loan_id IS NOT NULL))
);

CREATE INDEX idx_documents_member_id ON documents(member_id);
CREATE INDEX idx_documents_loan_id ON documents(loan_id);
CREATE INDEX idx_documents_type ON documents(document_type);
CREATE INDEX idx_documents_status ON documents(status);
```

### Transactions Table
```sql
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_number VARCHAR(20) UNIQUE NOT NULL,
    member_id UUID REFERENCES members(id),
    transaction_type VARCHAR(50) NOT NULL,
    category VARCHAR(50) NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    description TEXT NOT NULL,
    reference_id UUID,
    reference_type VARCHAR(50),
    payment_method VARCHAR(50),
    payment_reference VARCHAR(100),
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    processed_by UUID REFERENCES users(id),
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT transactions_type_check CHECK (transaction_type IN ('credit', 'debit')),
    CONSTRAINT transactions_category_check CHECK (category IN ('contribution', 'loan_disbursement', 'loan_payment', 'fee', 'penalty', 'profit_distribution', 'refund')),
    CONSTRAINT transactions_status_check CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    CONSTRAINT transactions_amount_not_zero CHECK (amount != 0)
);

CREATE INDEX idx_transactions_member_id ON transactions(member_id);
CREATE INDEX idx_transactions_type ON transactions(transaction_type);
CREATE INDEX idx_transactions_category ON transactions(category);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_date ON transactions(created_at);
CREATE INDEX idx_transactions_reference ON transactions(reference_id, reference_type);
```

### Profit Distributions Table
```sql
CREATE TABLE profit_distributions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
    distribution_period VARCHAR(20) NOT NULL,
    total_profit DECIMAL(12,2) NOT NULL,
    member_share DECIMAL(12,2) NOT NULL,
    contribution_basis DECIMAL(12,2) NOT NULL,
    distribution_rate DECIMAL(5,4) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'calculated',
    calculated_date DATE NOT NULL DEFAULT CURRENT_DATE,
    distributed_date DATE,
    payment_method VARCHAR(50),
    payment_reference VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT profit_distributions_status_check CHECK (status IN ('calculated', 'approved', 'distributed', 'cancelled')),
    CONSTRAINT profit_distributions_share_positive CHECK (member_share >= 0),
    CONSTRAINT profit_distributions_rate_positive CHECK (distribution_rate >= 0)
);

CREATE INDEX idx_profit_distributions_member_id ON profit_distributions(member_id);
CREATE INDEX idx_profit_distributions_period ON profit_distributions(distribution_period);
CREATE INDEX idx_profit_distributions_status ON profit_distributions(status);
```

### Support Tickets Table
```sql
CREATE TABLE support_tickets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_number VARCHAR(20) UNIQUE NOT NULL,
    member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
    subject VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(50) NOT NULL,
    priority VARCHAR(20) NOT NULL DEFAULT 'medium',
    status VARCHAR(50) NOT NULL DEFAULT 'open',
    assigned_to UUID REFERENCES users(id),
    resolution TEXT,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT support_tickets_category_check CHECK (category IN ('account', 'contribution', 'loan', 'payment', 'technical', 'general')),
    CONSTRAINT support_tickets_priority_check CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    CONSTRAINT support_tickets_status_check CHECK (status IN ('open', 'in_progress', 'resolved', 'closed', 'cancelled'))
);

CREATE INDEX idx_support_tickets_member_id ON support_tickets(member_id);
CREATE INDEX idx_support_tickets_status ON support_tickets(status);
CREATE INDEX idx_support_tickets_priority ON support_tickets(priority);
CREATE INDEX idx_support_tickets_assigned ON support_tickets(assigned_to);
```

### Support Messages Table
```sql
CREATE TABLE support_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_id UUID NOT NULL REFERENCES support_tickets(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES users(id),
    message TEXT NOT NULL,
    is_internal BOOLEAN DEFAULT FALSE,
    attachments JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT support_messages_message_not_empty CHECK (LENGTH(TRIM(message)) > 0)
);

CREATE INDEX idx_support_messages_ticket_id ON support_messages(ticket_id);
CREATE INDEX idx_support_messages_sender_id ON support_messages(sender_id);
CREATE INDEX idx_support_messages_created_at ON support_messages(created_at);
```

### Notifications Table
```sql
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    data JSONB,
    channels VARCHAR(100)[] NOT NULL DEFAULT '{}',
    read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,
    scheduled_for TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT notifications_type_check CHECK (type IN ('contribution_due', 'payment_received', 'loan_approved', 'loan_payment_due', 'profit_distribution', 'system_announcement', 'support_update')),
    CONSTRAINT notifications_channels_valid CHECK (channels <@ ARRAY['push', 'email', 'sms', 'in_app'])
);

CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_read ON notifications(user_id, read);
CREATE INDEX idx_notifications_scheduled ON notifications(scheduled_for) WHERE scheduled_for IS NOT NULL;
```

### Audit Logs Table
```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT audit_logs_action_check CHECK (action IN ('INSERT', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'FAILED_LOGIN', 'PASSWORD_CHANGE', 'PERMISSION_CHANGE'))
);

CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_table_name ON audit_logs(table_name);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_audit_logs_record_id ON audit_logs(record_id);
```

## Views and Functions

### Member Dashboard View
```sql
CREATE VIEW member_dashboard AS
SELECT 
    m.id,
    m.member_number,
    up.first_name,
    up.last_name,
    m.status,
    m.total_contributions,
    m.current_debt,
    COALESCE(active_loans.count, 0) as active_loans_count,
    COALESCE(pending_contributions.amount, 0) as pending_contributions,
    COALESCE(overdue_payments.amount, 0) as overdue_payments
FROM members m
JOIN user_profiles up ON m.user_id = up.user_id
LEFT JOIN (
    SELECT member_id, COUNT(*) as count
    FROM loans 
    WHERE status = 'active'
    GROUP BY member_id
) active_loans ON m.id = active_loans.member_id
LEFT JOIN (
    SELECT member_id, SUM(amount) as amount
    FROM contributions 
    WHERE status = 'pending'
    GROUP BY member_id
) pending_contributions ON m.id = pending_contributions.member_id
LEFT JOIN (
    SELECT l.member_id, SUM(lp.amount) as amount
    FROM loan_payments lp
    JOIN loans l ON lp.loan_id = l.id
    WHERE lp.status = 'overdue'
    GROUP BY l.member_id
) overdue_payments ON m.id = overdue_payments.member_id;
```

### Financial Summary Function
```sql
CREATE OR REPLACE FUNCTION get_financial_summary(
    start_date DATE DEFAULT NULL,
    end_date DATE DEFAULT NULL
)
RETURNS TABLE (
    total_contributions DECIMAL(12,2),
    total_loans_disbursed DECIMAL(12,2),
    total_loan_payments DECIMAL(12,2),
    outstanding_loans DECIMAL(12,2),
    profit_distributed DECIMAL(12,2)
) AS $$
BEGIN
    IF start_date IS NULL THEN
        start_date := DATE_TRUNC('year', CURRENT_DATE);
    END IF;
    
    IF end_date IS NULL THEN
        end_date := CURRENT_DATE;
    END IF;
    
    RETURN QUERY
    SELECT 
        COALESCE(SUM(CASE WHEN t.category = 'contribution' THEN t.amount END), 0) as total_contributions,
        COALESCE(SUM(CASE WHEN t.category = 'loan_disbursement' THEN t.amount END), 0) as total_loans_disbursed,
        COALESCE(SUM(CASE WHEN t.category = 'loan_payment' THEN t.amount END), 0) as total_loan_payments,
        COALESCE((SELECT SUM(remaining_balance) FROM loans WHERE status = 'active'), 0) as outstanding_loans,
        COALESCE(SUM(CASE WHEN t.category = 'profit_distribution' THEN t.amount END), 0) as profit_distributed
    FROM transactions t
    WHERE t.created_at::DATE BETWEEN start_date AND end_date
    AND t.status = 'completed';
END;
$$ LANGUAGE plpgsql;
```

## Triggers and Constraints

### Update Timestamps Trigger
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply to all tables with updated_at column
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_members_updated_at BEFORE UPDATE ON members
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ... (apply to other tables)
```

### Audit Trigger
```sql
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit_logs (action, table_name, record_id, new_values)
        VALUES ('INSERT', TG_TABLE_NAME, NEW.id, row_to_json(NEW));
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_logs (action, table_name, record_id, old_values, new_values)
        VALUES ('UPDATE', TG_TABLE_NAME, NEW.id, row_to_json(OLD), row_to_json(NEW));
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audit_logs (action, table_name, record_id, old_values)
        VALUES ('DELETE', TG_TABLE_NAME, OLD.id, row_to_json(OLD));
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Apply audit trigger to sensitive tables
CREATE TRIGGER audit_members AFTER INSERT OR UPDATE OR DELETE ON members
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_loans AFTER INSERT OR UPDATE OR DELETE ON loans
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
```

This database schema provides a comprehensive foundation for the financial association management system with proper normalization, indexing, and data integrity constraints.
