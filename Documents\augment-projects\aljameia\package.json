{"name": "aljameia-financial-association", "version": "1.0.0", "description": "Comprehensive financial association management system with mobile and web applications", "private": true, "workspaces": ["backend", "frontend"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "npm run dev --workspace=backend", "dev:frontend": "npm run dev --workspace=frontend", "build": "npm run build --workspaces", "build:backend": "npm run build --workspace=backend", "build:frontend": "npm run build --workspace=frontend", "test": "npm run test --workspaces", "test:backend": "npm run test --workspace=backend", "test:frontend": "npm run test --workspace=frontend", "lint": "npm run lint --workspaces", "lint:fix": "npm run lint:fix --workspaces", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "db:migrate": "npm run db:migrate --workspace=backend", "db:seed": "npm run db:seed --workspace=backend", "deploy:staging": "npm run build && npm run deploy:staging --workspaces", "deploy:production": "npm run build && npm run deploy:production --workspaces"}, "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{js,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/aljameia/financial-association.git"}, "keywords": ["financial", "association", "management", "mobile", "web", "arabic", "bilingual", "loans", "contributions"], "author": "Al-Jameia Development Team", "license": "PROPRIETARY"}