# Security Protocols Documentation

## Overview

The Al-Jameia Financial Association Management System implements comprehensive security measures to protect sensitive financial data and ensure compliance with industry standards including PCI DSS, GDPR, and local banking regulations.

## Security Architecture

### Defense in Depth Strategy

```
┌─────────────────────────────────────────────────────────────┐
│                    External Security Layer                  │
│  ┌─────────────────────────────────────────────────────┐   │
│  │              Network Security Layer                 │   │
│  │  ┌─────────────────────────────────────────────┐   │   │
│  │  │            Application Security Layer       │   │   │
│  │  │  ┌─────────────────────────────────────┐   │   │   │
│  │  │  │        Data Security Layer          │   │   │   │
│  │  │  │  ┌─────────────────────────────┐   │   │   │   │
│  │  │  │  │    Infrastructure Security  │   │   │   │   │
│  │  │  │  └─────────────────────────────┘   │   │   │   │
│  │  │  └─────────────────────────────────────┘   │   │   │
│  │  └─────────────────────────────────────────────┘   │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## Authentication & Authorization

### Multi-Factor Authentication (2FA)

#### TOTP Implementation
```typescript
// 2FA Setup Process
interface TwoFactorSetup {
  secret: string;           // Base32 encoded secret
  qrCodeUrl: string;       // QR code for authenticator apps
  backupCodes: string[];   // 10 single-use backup codes
  algorithm: 'SHA1';       // HMAC algorithm
  digits: 6;               // Code length
  period: 30;              // Time window in seconds
}

// 2FA Verification
interface TwoFactorVerification {
  code: string;            // 6-digit TOTP code
  timestamp: number;       // Current timestamp
  window: number;          // Acceptable time drift (±1 period)
}
```

#### JWT Token Security
```typescript
// JWT Configuration
const jwtConfig = {
  algorithm: 'RS256',                    // RSA with SHA-256
  accessTokenExpiry: '15m',              // Short-lived access tokens
  refreshTokenExpiry: '7d',              // Longer-lived refresh tokens
  issuer: 'aljameia-api',
  audience: 'aljameia-clients',
  keyRotation: '30d',                    // Regular key rotation
  
  // Token payload structure
  payload: {
    sub: 'user_id',                      // Subject (user ID)
    iat: 'issued_at',                    // Issued at timestamp
    exp: 'expires_at',                   // Expiration timestamp
    aud: 'audience',                     // Intended audience
    iss: 'issuer',                       // Token issuer
    jti: 'jwt_id',                       // Unique token ID
    role: 'user_role',                   // User role
    permissions: ['array_of_permissions'] // Specific permissions
  }
};
```

### Role-Based Access Control (RBAC)

#### Permission Matrix
```typescript
enum Roles {
  MEMBER = 'member',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
  FINANCIAL_OFFICER = 'financial_officer',
  SUPPORT_AGENT = 'support_agent'
}

enum Permissions {
  // Member permissions
  READ_OWN_PROFILE = 'read:own_profile',
  UPDATE_OWN_PROFILE = 'update:own_profile',
  READ_OWN_CONTRIBUTIONS = 'read:own_contributions',
  READ_OWN_LOANS = 'read:own_loans',
  APPLY_FOR_LOAN = 'apply:loan',
  UPLOAD_DOCUMENTS = 'upload:documents',
  CREATE_SUPPORT_TICKET = 'create:support_ticket',
  
  // Admin permissions
  READ_ALL_MEMBERS = 'read:all_members',
  UPDATE_MEMBER_STATUS = 'update:member_status',
  APPROVE_LOANS = 'approve:loans',
  MANAGE_CONTRIBUTIONS = 'manage:contributions',
  VIEW_FINANCIAL_REPORTS = 'view:financial_reports',
  MANAGE_SUPPORT_TICKETS = 'manage:support_tickets',
  
  // Super admin permissions
  MANAGE_USERS = 'manage:users',
  SYSTEM_CONFIGURATION = 'system:configuration',
  VIEW_AUDIT_LOGS = 'view:audit_logs',
  MANAGE_ROLES = 'manage:roles'
}

// Role-Permission mapping
const rolePermissions: Record<Roles, Permissions[]> = {
  [Roles.MEMBER]: [
    Permissions.READ_OWN_PROFILE,
    Permissions.UPDATE_OWN_PROFILE,
    Permissions.READ_OWN_CONTRIBUTIONS,
    Permissions.READ_OWN_LOANS,
    Permissions.APPLY_FOR_LOAN,
    Permissions.UPLOAD_DOCUMENTS,
    Permissions.CREATE_SUPPORT_TICKET
  ],
  [Roles.ADMIN]: [
    ...rolePermissions[Roles.MEMBER],
    Permissions.READ_ALL_MEMBERS,
    Permissions.UPDATE_MEMBER_STATUS,
    Permissions.APPROVE_LOANS,
    Permissions.MANAGE_CONTRIBUTIONS,
    Permissions.VIEW_FINANCIAL_REPORTS,
    Permissions.MANAGE_SUPPORT_TICKETS
  ],
  [Roles.SUPER_ADMIN]: [
    ...rolePermissions[Roles.ADMIN],
    Permissions.MANAGE_USERS,
    Permissions.SYSTEM_CONFIGURATION,
    Permissions.VIEW_AUDIT_LOGS,
    Permissions.MANAGE_ROLES
  ]
};
```

## Data Encryption

### Encryption at Rest

#### Database Encryption
```sql
-- Transparent Data Encryption (TDE) configuration
ALTER DATABASE aljameia_db SET encryption = 'AES-256';

-- Column-level encryption for sensitive data
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    -- Encrypted columns
    ssn BYTEA, -- Encrypted with AES-256-GCM
    bank_account BYTEA, -- Encrypted with AES-256-GCM
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Encryption functions
CREATE OR REPLACE FUNCTION encrypt_sensitive_data(data TEXT, key_id TEXT)
RETURNS BYTEA AS $$
BEGIN
    -- Use pgcrypto extension for encryption
    RETURN pgp_sym_encrypt(data, get_encryption_key(key_id), 'cipher-algo=aes256');
END;
$$ LANGUAGE plpgsql;
```

#### File Storage Encryption
```typescript
// AWS S3 Server-Side Encryption configuration
const s3Config = {
  serverSideEncryption: 'aws:kms',
  sseKmsKeyId: process.env.AWS_KMS_KEY_ID,
  bucketKeyEnabled: true,
  
  // Client-side encryption for highly sensitive documents
  clientSideEncryption: {
    algorithm: 'AES-256-GCM',
    keyDerivation: 'PBKDF2',
    iterations: 100000,
    saltLength: 32
  }
};

// Document encryption before upload
class DocumentEncryption {
  static async encryptDocument(
    fileBuffer: Buffer, 
    password: string
  ): Promise<EncryptedDocument> {
    const salt = crypto.randomBytes(32);
    const iv = crypto.randomBytes(16);
    
    // Derive key using PBKDF2
    const key = crypto.pbkdf2Sync(password, salt, 100000, 32, 'sha256');
    
    // Encrypt file content
    const cipher = crypto.createCipher('aes-256-gcm', key, iv);
    const encrypted = Buffer.concat([
      cipher.update(fileBuffer),
      cipher.final()
    ]);
    
    const authTag = cipher.getAuthTag();
    
    return {
      encryptedData: encrypted,
      salt: salt.toString('base64'),
      iv: iv.toString('base64'),
      authTag: authTag.toString('base64'),
      algorithm: 'aes-256-gcm'
    };
  }
}
```

### Encryption in Transit

#### TLS Configuration
```typescript
// Express.js TLS configuration
const tlsOptions = {
  // TLS version
  secureProtocol: 'TLSv1_3_method',
  
  // Cipher suites (ordered by preference)
  ciphers: [
    'TLS_AES_256_GCM_SHA384',
    'TLS_CHACHA20_POLY1305_SHA256',
    'TLS_AES_128_GCM_SHA256',
    'ECDHE-RSA-AES256-GCM-SHA384',
    'ECDHE-RSA-AES128-GCM-SHA256'
  ].join(':'),
  
  // Security headers
  securityHeaders: {
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
    'Referrer-Policy': 'strict-origin-when-cross-origin'
  }
};
```

## Input Validation & Sanitization

### SQL Injection Prevention
```typescript
// Parameterized queries using TypeORM
class MemberRepository {
  async findByEmail(email: string): Promise<Member | null> {
    // Safe: Uses parameterized query
    return this.repository.findOne({
      where: { email: email },
      relations: ['profile', 'contributions']
    });
  }
  
  async searchMembers(searchTerm: string): Promise<Member[]> {
    // Safe: Uses query builder with parameters
    return this.repository
      .createQueryBuilder('member')
      .leftJoinAndSelect('member.profile', 'profile')
      .where('profile.firstName ILIKE :search OR profile.lastName ILIKE :search', {
        search: `%${searchTerm}%`
      })
      .getMany();
  }
}

// Input validation middleware
const validateInput = (schema: Joi.Schema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.body);
    
    if (error) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid input data',
          details: error.details
        }
      });
    }
    
    req.body = value; // Use sanitized values
    next();
  };
};
```

### XSS Prevention
```typescript
// Content Security Policy
const cspConfig = {
  directives: {
    defaultSrc: ["'self'"],
    scriptSrc: ["'self'", "'unsafe-inline'", "https://trusted-cdn.com"],
    styleSrc: ["'self'", "'unsafe-inline'"],
    imgSrc: ["'self'", "data:", "https:"],
    connectSrc: ["'self'", "https://api.aljameia.com"],
    fontSrc: ["'self'", "https://fonts.googleapis.com"],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'"],
    frameSrc: ["'none'"]
  }
};

// Input sanitization
import DOMPurify from 'dompurify';

class InputSanitizer {
  static sanitizeHtml(input: string): string {
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
      ALLOWED_ATTR: []
    });
  }
  
  static sanitizeText(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .trim();
  }
}
```

## Session Management

### Secure Session Configuration
```typescript
// Session configuration
const sessionConfig = {
  secret: process.env.SESSION_SECRET, // Strong random secret
  name: 'aljameia_session',
  
  cookie: {
    secure: true,           // HTTPS only
    httpOnly: true,         // Prevent XSS
    maxAge: 30 * 60 * 1000, // 30 minutes
    sameSite: 'strict'      // CSRF protection
  },
  
  // Session store (Redis)
  store: new RedisStore({
    client: redisClient,
    prefix: 'sess:',
    ttl: 1800 // 30 minutes
  }),
  
  resave: false,
  saveUninitialized: false,
  rolling: true // Reset expiry on activity
};

// Session security middleware
const sessionSecurity = (req: Request, res: Response, next: NextFunction) => {
  // Regenerate session ID on login
  if (req.path === '/auth/login' && req.method === 'POST') {
    req.session.regenerate((err) => {
      if (err) {
        return next(err);
      }
      next();
    });
  } else {
    next();
  }
};
```

## API Security

### Rate Limiting
```typescript
// Rate limiting configuration
const rateLimitConfig = {
  windowMs: 60 * 60 * 1000, // 1 hour
  max: (req: Request) => {
    // Different limits based on user role
    const user = req.user as AuthenticatedUser;
    switch (user?.role) {
      case 'super_admin': return 10000;
      case 'admin': return 5000;
      case 'member': return 1000;
      default: return 100; // Unauthenticated users
    }
  },
  
  // Custom key generator
  keyGenerator: (req: Request) => {
    return req.user?.id || req.ip;
  },
  
  // Rate limit headers
  standardHeaders: true,
  legacyHeaders: false,
  
  // Custom handler
  handler: (req: Request, res: Response) => {
    res.status(429).json({
      success: false,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Too many requests, please try again later',
        retryAfter: Math.round(req.rateLimit.resetTime / 1000)
      }
    });
  }
};
```

### API Key Management
```typescript
// API key structure
interface APIKey {
  id: string;
  keyHash: string;        // SHA-256 hash of the key
  name: string;           // Human-readable name
  permissions: string[];  // Specific permissions
  rateLimit: number;      // Custom rate limit
  ipWhitelist?: string[]; // Optional IP restrictions
  expiresAt?: Date;       // Optional expiration
  lastUsed?: Date;        // Track usage
  isActive: boolean;      // Enable/disable key
}

// API key validation middleware
const validateAPIKey = async (req: Request, res: Response, next: NextFunction) => {
  const apiKey = req.headers['x-api-key'] as string;
  
  if (!apiKey) {
    return res.status(401).json({
      success: false,
      error: { code: 'API_KEY_REQUIRED', message: 'API key is required' }
    });
  }
  
  const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');
  const storedKey = await APIKeyRepository.findByHash(keyHash);
  
  if (!storedKey || !storedKey.isActive) {
    return res.status(401).json({
      success: false,
      error: { code: 'INVALID_API_KEY', message: 'Invalid or inactive API key' }
    });
  }
  
  // Check expiration
  if (storedKey.expiresAt && storedKey.expiresAt < new Date()) {
    return res.status(401).json({
      success: false,
      error: { code: 'API_KEY_EXPIRED', message: 'API key has expired' }
    });
  }
  
  // Check IP whitelist
  if (storedKey.ipWhitelist && !storedKey.ipWhitelist.includes(req.ip)) {
    return res.status(403).json({
      success: false,
      error: { code: 'IP_NOT_ALLOWED', message: 'IP address not whitelisted' }
    });
  }
  
  // Update last used timestamp
  await APIKeyRepository.updateLastUsed(storedKey.id);
  
  req.apiKey = storedKey;
  next();
};
```

## Audit Logging

### Comprehensive Audit Trail
```typescript
// Audit log structure
interface AuditLog {
  id: string;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
  ipAddress: string;
  userAgent: string;
  action: AuditAction;
  resource: string;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  result: 'success' | 'failure';
  errorMessage?: string;
  metadata?: Record<string, any>;
}

enum AuditAction {
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  FAILED_LOGIN = 'FAILED_LOGIN',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  PROFILE_UPDATE = 'PROFILE_UPDATE',
  LOAN_APPLICATION = 'LOAN_APPLICATION',
  LOAN_APPROVAL = 'LOAN_APPROVAL',
  PAYMENT_MADE = 'PAYMENT_MADE',
  DOCUMENT_UPLOAD = 'DOCUMENT_UPLOAD',
  ADMIN_ACTION = 'ADMIN_ACTION'
}

// Audit logging middleware
const auditLogger = (action: AuditAction, resource: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      // Log the action after response
      const auditLog: AuditLog = {
        id: uuidv4(),
        timestamp: new Date(),
        userId: req.user?.id,
        sessionId: req.sessionID,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent') || '',
        action,
        resource,
        resourceId: req.params.id,
        oldValues: req.originalData,
        newValues: req.body,
        result: res.statusCode < 400 ? 'success' : 'failure',
        errorMessage: res.statusCode >= 400 ? data : undefined
      };
      
      AuditLogRepository.create(auditLog);
      
      return originalSend.call(this, data);
    };
    
    next();
  };
};
```

## Security Monitoring

### Real-time Threat Detection
```typescript
// Security event detection
class SecurityMonitor {
  static async detectSuspiciousActivity(req: Request): Promise<SecurityAlert[]> {
    const alerts: SecurityAlert[] = [];
    
    // Multiple failed login attempts
    const failedAttempts = await this.getFailedLoginAttempts(req.ip, '1h');
    if (failedAttempts > 5) {
      alerts.push({
        type: 'BRUTE_FORCE_ATTEMPT',
        severity: 'HIGH',
        description: `${failedAttempts} failed login attempts from ${req.ip}`,
        ipAddress: req.ip,
        timestamp: new Date()
      });
    }
    
    // Unusual access patterns
    const accessPattern = await this.analyzeAccessPattern(req.user?.id);
    if (accessPattern.isUnusual) {
      alerts.push({
        type: 'UNUSUAL_ACCESS_PATTERN',
        severity: 'MEDIUM',
        description: 'User accessing from unusual location or time',
        userId: req.user?.id,
        timestamp: new Date()
      });
    }
    
    // Large data requests
    if (req.query.limit && parseInt(req.query.limit as string) > 1000) {
      alerts.push({
        type: 'LARGE_DATA_REQUEST',
        severity: 'MEDIUM',
        description: 'Request for unusually large amount of data',
        userId: req.user?.id,
        timestamp: new Date()
      });
    }
    
    return alerts;
  }
  
  static async handleSecurityAlert(alert: SecurityAlert): Promise<void> {
    // Log the alert
    await SecurityAlertRepository.create(alert);
    
    // Take automated action based on severity
    switch (alert.severity) {
      case 'HIGH':
        // Block IP address temporarily
        await this.blockIPAddress(alert.ipAddress, '1h');
        // Notify security team immediately
        await this.notifySecurityTeam(alert);
        break;
        
      case 'MEDIUM':
        // Require additional verification
        await this.requireAdditionalVerification(alert.userId);
        break;
        
      case 'LOW':
        // Just log for analysis
        break;
    }
  }
}
```

## Compliance & Standards

### PCI DSS Compliance Checklist
- ✅ Install and maintain firewall configuration
- ✅ Do not use vendor-supplied defaults for passwords
- ✅ Protect stored cardholder data with encryption
- ✅ Encrypt transmission of cardholder data across networks
- ✅ Use and regularly update anti-virus software
- ✅ Develop and maintain secure systems and applications
- ✅ Restrict access to cardholder data by business need-to-know
- ✅ Assign unique ID to each person with computer access
- ✅ Restrict physical access to cardholder data
- ✅ Track and monitor all access to network resources
- ✅ Regularly test security systems and processes
- ✅ Maintain information security policy

### GDPR Compliance Features
- ✅ Data minimization and purpose limitation
- ✅ Consent management system
- ✅ Right to access personal data
- ✅ Right to rectification
- ✅ Right to erasure (right to be forgotten)
- ✅ Data portability
- ✅ Privacy by design and by default
- ✅ Data breach notification procedures

This comprehensive security protocol ensures the Al-Jameia Financial Association Management System meets the highest security standards for financial applications.
