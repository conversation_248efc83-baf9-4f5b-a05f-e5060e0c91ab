# API Specification Documentation

## Overview

The Al-Jameia Financial Association Management System API is a RESTful service that provides secure endpoints for member management, financial operations, loan processing, and administrative functions.

## Base Configuration

- **Base URL**: `https://api.aljameia.com/v1`
- **Authentication**: JWT Bearer Token + 2FA
- **Content-Type**: `application/json`
- **Rate Limiting**: 1000 requests per hour per user
- **API Version**: v1

## Authentication

### Login
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "requiresTwoFactor": true,
    "tempToken": "temp_jwt_token_here",
    "message": "2FA verification required"
  }
}
```

### Two-Factor Authentication
```http
POST /auth/2fa/verify
Authorization: Bearer temp_jwt_token_here
Content-Type: application/json

{
  "code": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "accessToken": "jwt_access_token_here",
    "refreshToken": "jwt_refresh_token_here",
    "expiresIn": 3600,
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "role": "member",
      "profile": {
        "firstName": "Ahmed",
        "lastName": "Al-Rashid",
        "memberNumber": "MEM001"
      }
    }
  }
}
```

### Refresh Token
```http
POST /auth/refresh
Content-Type: application/json

{
  "refreshToken": "jwt_refresh_token_here"
}
```

## Member Management

### Get Member Profile
```http
GET /members/profile
Authorization: Bearer jwt_access_token_here
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "memberNumber": "MEM001",
    "personalInfo": {
      "firstName": "Ahmed",
      "lastName": "Al-Rashid",
      "firstNameAr": "أحمد",
      "lastNameAr": "الراشد",
      "email": "<EMAIL>",
      "phone": "+************",
      "dateOfBirth": "1985-03-15",
      "nationality": "Saudi Arabia"
    },
    "membershipInfo": {
      "joinDate": "2020-01-15",
      "status": "active",
      "membershipType": "regular",
      "monthlyContribution": 500.00
    },
    "financialSummary": {
      "totalContributions": 18000.00,
      "currentDebt": 5000.00,
      "creditScore": 750,
      "activeLoans": 1
    }
  }
}
```

### Update Member Profile
```http
PUT /members/profile
Authorization: Bearer jwt_access_token_here
Content-Type: application/json

{
  "personalInfo": {
    "phone": "+************",
    "address": "New Address, Riyadh"
  }
}
```

### Get Member Dashboard
```http
GET /members/dashboard
Authorization: Bearer jwt_access_token_here
```

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalContributions": 18000.00,
      "pendingContributions": 500.00,
      "activeLoans": 1,
      "overduePayments": 0.00,
      "nextPaymentDue": "2024-01-15",
      "creditScore": 750
    },
    "recentTransactions": [
      {
        "id": "uuid",
        "type": "contribution",
        "amount": 500.00,
        "date": "2023-12-15",
        "status": "completed",
        "description": "Monthly contribution - December 2023"
      }
    ],
    "notifications": [
      {
        "id": "uuid",
        "type": "payment_reminder",
        "title": "Payment Due Reminder",
        "message": "Your monthly contribution is due on January 15, 2024",
        "date": "2024-01-10",
        "read": false
      }
    ]
  }
}
```

## Financial Operations

### Get Contributions
```http
GET /members/contributions?page=1&limit=10&status=all&year=2023
Authorization: Bearer jwt_access_token_here
```

**Response:**
```json
{
  "success": true,
  "data": {
    "contributions": [
      {
        "id": "uuid",
        "amount": 500.00,
        "type": "monthly",
        "dueDate": "2023-12-15",
        "paidDate": "2023-12-14",
        "status": "paid",
        "paymentMethod": "bank_transfer",
        "paymentReference": "TXN123456"
      }
    ],
    "summary": {
      "totalPaid": 6000.00,
      "totalPending": 500.00,
      "totalOverdue": 0.00
    }
  },
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3
  }
}
```

### Make Contribution Payment
```http
POST /payments/contribute
Authorization: Bearer jwt_access_token_here
Content-Type: application/json

{
  "contributionId": "uuid",
  "amount": 500.00,
  "paymentMethod": "card",
  "paymentDetails": {
    "cardToken": "stripe_card_token_here"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "transactionId": "uuid",
    "paymentReference": "PAY123456",
    "status": "completed",
    "amount": 500.00,
    "processingFee": 15.00,
    "netAmount": 485.00,
    "processedAt": "2024-01-15T10:30:00Z"
  }
}
```

## Loan Management

### Get Loans
```http
GET /members/loans?status=active
Authorization: Bearer jwt_access_token_here
```

**Response:**
```json
{
  "success": true,
  "data": {
    "loans": [
      {
        "id": "uuid",
        "loanNumber": "LOAN001",
        "type": "personal",
        "principalAmount": 10000.00,
        "interestRate": 5.5,
        "termMonths": 24,
        "monthlyPayment": 450.00,
        "remainingBalance": 5000.00,
        "status": "active",
        "applicationDate": "2023-06-15",
        "disbursementDate": "2023-07-01",
        "maturityDate": "2025-07-01",
        "nextPaymentDue": "2024-02-01"
      }
    ]
  }
}
```

### Apply for Loan
```http
POST /loans/apply
Authorization: Bearer jwt_access_token_here
Content-Type: application/json

{
  "loanType": "personal",
  "requestedAmount": 15000.00,
  "termMonths": 36,
  "purpose": "Home renovation",
  "monthlyIncome": 8000.00,
  "employmentDetails": {
    "employer": "ABC Company",
    "position": "Software Engineer",
    "yearsEmployed": 3
  },
  "guarantorId": "uuid_optional"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "applicationId": "uuid",
    "loanNumber": "LOAN002",
    "status": "pending",
    "estimatedProcessingTime": "5-7 business days",
    "requiredDocuments": [
      "salary_certificate",
      "bank_statements",
      "id_copy"
    ],
    "message": "Your loan application has been submitted successfully. Please upload the required documents."
  }
}
```

### Get Loan Payment Schedule
```http
GET /loans/{loanId}/schedule
Authorization: Bearer jwt_access_token_here
```

**Response:**
```json
{
  "success": true,
  "data": {
    "loanId": "uuid",
    "loanNumber": "LOAN001",
    "schedule": [
      {
        "paymentNumber": 1,
        "dueDate": "2023-08-01",
        "amount": 450.00,
        "principalAmount": 400.00,
        "interestAmount": 50.00,
        "remainingBalance": 9600.00,
        "status": "paid",
        "paidDate": "2023-07-30"
      },
      {
        "paymentNumber": 2,
        "dueDate": "2023-09-01",
        "amount": 450.00,
        "principalAmount": 402.00,
        "interestAmount": 48.00,
        "remainingBalance": 9198.00,
        "status": "pending"
      }
    ]
  }
}
```

### Make Loan Payment
```http
POST /loans/{loanId}/payments
Authorization: Bearer jwt_access_token_here
Content-Type: application/json

{
  "amount": 450.00,
  "paymentMethod": "bank_transfer",
  "paymentReference": "TXN789012"
}
```

## Document Management

### Upload Document
```http
POST /documents/upload
Authorization: Bearer jwt_access_token_here
Content-Type: multipart/form-data

{
  "file": [binary_file_data],
  "documentType": "salary_certificate",
  "title": "Salary Certificate - December 2023",
  "relatedEntityType": "loan",
  "relatedEntityId": "uuid"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "documentId": "uuid",
    "filename": "salary_certificate_20231215.pdf",
    "fileSize": 245760,
    "uploadedAt": "2023-12-15T14:30:00Z",
    "status": "pending_review",
    "downloadUrl": "https://api.aljameia.com/v1/documents/uuid/download"
  }
}
```

### Get Documents
```http
GET /documents?type=all&status=approved&page=1&limit=10
Authorization: Bearer jwt_access_token_here
```

**Response:**
```json
{
  "success": true,
  "data": {
    "documents": [
      {
        "id": "uuid",
        "type": "salary_certificate",
        "title": "Salary Certificate - December 2023",
        "filename": "salary_certificate_20231215.pdf",
        "fileSize": 245760,
        "status": "approved",
        "uploadedAt": "2023-12-15T14:30:00Z",
        "expiryDate": "2024-12-15",
        "downloadUrl": "https://api.aljameia.com/v1/documents/uuid/download"
      }
    ]
  },
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 5,
    "totalPages": 1
  }
}
```

## Reports and Analytics

### Get Financial Report
```http
GET /reports/financial?startDate=2023-01-01&endDate=2023-12-31&format=json
Authorization: Bearer jwt_access_token_here
```

**Response:**
```json
{
  "success": true,
  "data": {
    "period": {
      "startDate": "2023-01-01",
      "endDate": "2023-12-31"
    },
    "summary": {
      "totalContributions": 6000.00,
      "totalLoanPayments": 5400.00,
      "totalProfitDistribution": 300.00,
      "outstandingBalance": 5000.00
    },
    "monthlyBreakdown": [
      {
        "month": "2023-01",
        "contributions": 500.00,
        "loanPayments": 450.00,
        "profitDistribution": 25.00
      }
    ],
    "downloadUrl": "https://api.aljameia.com/v1/reports/financial/download?token=temp_token"
  }
}
```

### Generate PDF Report
```http
POST /reports/generate
Authorization: Bearer jwt_access_token_here
Content-Type: application/json

{
  "reportType": "annual_statement",
  "year": 2023,
  "format": "pdf",
  "language": "ar"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "reportId": "uuid",
    "status": "generating",
    "estimatedTime": "2-3 minutes",
    "downloadUrl": "https://api.aljameia.com/v1/reports/uuid/download"
  }
}
```

## Notifications

### Get Notifications
```http
GET /notifications?read=false&page=1&limit=20
Authorization: Bearer jwt_access_token_here
```

**Response:**
```json
{
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "uuid",
        "type": "payment_reminder",
        "title": "Payment Due Reminder",
        "message": "Your monthly contribution of SAR 500.00 is due on January 15, 2024",
        "data": {
          "contributionId": "uuid",
          "amount": 500.00,
          "dueDate": "2024-01-15"
        },
        "read": false,
        "createdAt": "2024-01-10T09:00:00Z"
      }
    ]
  },
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 15,
    "totalPages": 1
  }
}
```

### Mark Notification as Read
```http
PUT /notifications/{notificationId}/read
Authorization: Bearer jwt_access_token_here
```

## Support System

### Create Support Ticket
```http
POST /support/tickets
Authorization: Bearer jwt_access_token_here
Content-Type: application/json

{
  "subject": "Issue with loan payment",
  "description": "I'm having trouble making my loan payment through the mobile app",
  "category": "technical",
  "priority": "medium"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "ticketId": "uuid",
    "ticketNumber": "TICKET001",
    "status": "open",
    "estimatedResponseTime": "24 hours",
    "createdAt": "2024-01-15T10:00:00Z"
  }
}
```

### Get Support Tickets
```http
GET /support/tickets?status=open&page=1&limit=10
Authorization: Bearer jwt_access_token_here
```

## Error Handling

### Standard Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "reason": "Invalid email format"
    }
  },
  "meta": {
    "timestamp": "2024-01-15T10:00:00Z",
    "requestId": "req_uuid_here"
  }
}
```

### Common Error Codes
- `AUTHENTICATION_REQUIRED` (401)
- `INSUFFICIENT_PERMISSIONS` (403)
- `RESOURCE_NOT_FOUND` (404)
- `VALIDATION_ERROR` (400)
- `RATE_LIMIT_EXCEEDED` (429)
- `INTERNAL_SERVER_ERROR` (500)
- `SERVICE_UNAVAILABLE` (503)

## Rate Limiting

- **Standard Users**: 1000 requests/hour
- **Premium Users**: 5000 requests/hour
- **Admin Users**: 10000 requests/hour

Rate limit headers included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642248000
```

## Webhooks (Admin Only)

### Payment Webhook
```http
POST /webhooks/payments
Content-Type: application/json
X-Webhook-Signature: sha256=signature_here

{
  "event": "payment.completed",
  "data": {
    "transactionId": "uuid",
    "memberId": "uuid",
    "amount": 500.00,
    "type": "contribution",
    "timestamp": "2024-01-15T10:00:00Z"
  }
}
```

This API specification provides comprehensive endpoints for all major functionality of the financial association management system.
